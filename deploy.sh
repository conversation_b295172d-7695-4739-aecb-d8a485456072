#!/bin/bash

# Towasl Backend Deployment Script

echo "🚀 Starting Towasl Backend Deployment..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Navigate to functions directory
cd functions

echo "📦 Installing dependencies..."
npm install

echo "🔧 Building functions..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

echo "✅ Build successful!"

# Go back to root directory
cd ..

echo "🔐 Checking environment variables..."
echo "Please make sure you have already set the following environment variables:"
echo "firebase functions:config:set msegat.username=\"YOUR_MSEGAT_USERNAME\""
echo "firebase functions:config:set msegat.api_key=\"YOUR_MSEGAT_API_KEY\""
echo "firebase functions:config:set msegat.sender_id=\"YOUR_SENDER_ID\""
echo "firebase functions:config:set msegat.message_template=\"رمز التحقق: xxxx\""
echo ""
echo "⚠️  IMPORTANT: Replace YOUR_* placeholders with actual Msegat credentials"
echo ""
echo "💡 You can verify your current config with: firebase functions:config:get"

read -p "Have you set the environment variables? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please set the environment variables first."
    exit 1
fi

echo "🔥 Deploying to Firebase..."
firebase deploy

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo "🎉 Your functions are now live!"
    echo ""
    echo "Function URLs:"
    echo "- sendOtp: https://us-central1-towasl.cloudfunctions.net/sendOtp"
    echo "- verifyOtpAndSignupLogin: https://us-central1-towasl.cloudfunctions.net/verifyOtpAndSignupLogin"
else
    echo "❌ Deployment failed. Please check the errors above."
    exit 1
fi
